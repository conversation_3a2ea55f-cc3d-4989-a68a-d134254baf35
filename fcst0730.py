# -*- coding: utf-8 -*-
"""
backend_snapshot.py
后端自持快照 /refresh
前端点击刷新即可拿结果，无需回传旧数据
替换过滤逻辑为 fcst0723.py 版本
"""

import re
import difflib
import json
import traceback
from pathlib import Path
from datetime import datetime
from collections import OrderedDict

from flask import Flask, jsonify
from flask_cors import CORS
from docx import Document

# ---------- 全局辅助函数：取警报最晚时间 ----------
def latest_time(content: str):
    """
    返回警报正文中最晚的 datetime；如无时间则返回 None（不过滤）。
    规则：
    1. 抓取全文所有“日+时分”（支持多种写法）
    2. 无日 -> 默认今天
    3. 日 ≥20 且大于当前日 -> 视为上个月
    4. 取最晚时间；无任何时间 → None
    """
    now = datetime.now()
    latest = None

    # 日（可选月）
    day_pat = re.compile(r'(?:(\d{1,2})月)?(\d{1,2})日')
    # 时分多种写法
    time_pat = re.compile(
        r'(?<!\d)(\d{1,2})(?::|：|时|点)(\d{2})?(?=分|点|时|[^\d]|$)'
    )

    days = [(m.group(1), m.group(2)) for m in day_pat.finditer(content)]
    if not days:
        days = [(None, str(now.day))]

    times = []
    for m in time_pat.finditer(content):
        h = int(m.group(1))
        m = int(m.group(2) or 0)
        times.append((h, m))

    if not times:
        return None

    def resolve_date(day_raw, month_raw=None):
        day = int(day_raw)
        month = int(month_raw) if month_raw else now.month
        year = now.year
        if day >= 20 and day > now.day:
            month -= 1
            if month == 0:
                month = 12
                year -= 1
        try:
            return datetime(year, month, day)
        except ValueError:
            return None

    for month_raw, day_raw in days:
        base = resolve_date(day_raw, month_raw)
        if not base:
            continue
        for h, m in times:
            dt = base.replace(hour=h, minute=m)
            if latest is None or dt > latest:
                latest = dt
    return latest

# ---------- 路径 ----------
ROOT_MDRS = Path(r"示例\示例\mdrs")
ROOT_APT  = Path(r"示例\示例\机场警报")
ROOT_TERM = Path(r"示例\示例\终端区警报")

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

ICAO_NAME = {
    "ZBAA": "首都", "ZBAD": "大兴", "ZBTJ": "天津", "ZBSJ": "石家庄",
    "ZBYN": "太原", "ZBHH": "呼和浩特", "ZBLA": "海拉尔",
    "ZSSS": "虹桥", "ZSPD": "浦东", "ZSNJ": "南京", "ZSHC": "杭州",
    "ZSNB": "宁波", "ZSWZ": "温州", "ZSOF": "合肥", "ZSFZ": "福州",
    "ZSAM": "厦门", "ZSCN": "南昌", "ZSJN": "济南", "ZSQD": "青岛",
    "ZSYT": "烟台", "ZSWF": "威海", "ZHCC": "郑州", "ZHHH": "武汉",
    "ZGHA": "长沙", "ZGGG": "广州", "ZGSZ": "深圳", "ZGSD": "珠海",
    "ZGOW": "汕头", "ZGZJ": "湛江", "ZGNN": "南宁", "ZGKL": "桂林",
    "ZJHK": "海口", "ZJSY": "三亚", "ZUCK": "重庆", "ZUUU": "成都",
    "ZUTF": "天府", "ZUGY": "贵阳", "ZPPP": "昆明", "ZULS": "拉萨",
    "ZLXY": "西安", "ZLLL": "兰州", "ZLXN": "西宁", "ZLIC": "银川",
    "ZWWW": "乌鲁木齐", "ZWSH": "喀什", "ZWTN": "和田",
    "ZWYN": "伊犁", "ZWTC": "塔城", "ZWAT": "阿勒泰"
}

SNAPSHOT = Path(__file__).with_name("snapshot_raw.json")

# ---------- 工具 ----------
def list_docx(root: Path):
    return [p for p in root.rglob('*.docx') if p.is_file() and not p.name.startswith('~$')]

# ---------- MDRS ----------
def parse_mdrs(file_path: Path):
    try:
        doc = Document(file_path)
    except Exception:
        return {"airports": [], "terminal_areas": [], "routes": []}
    if not doc.tables:
        return {"airports": [], "terminal_areas": [], "routes": []}
    table = doc.tables[0]
    airports, terminals, routes = [], [], []
    current = None
    for row in table.rows[2:]:
        cells = [c.text.strip() for c in row.cells]
        if len(cells) < 8:
            continue
        region, weather, intensity, period, *_ = cells
        item = {"name": region, "content": f"{period}，{intensity}{weather}".strip("，")}
        if "机场" in region:
            airports.append(item)
        elif "终端区" in region:
            terminals.append(item)
        elif "航路" in region:
            routes.append(item)
    return {"airports": airports, "terminal_areas": terminals, "routes": routes}

# ---------- 警报 ----------
def parse_alert(file_path: Path):
    try:
        doc = Document(file_path)
    except Exception:
        return None
    lines = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
    blacklist = ["天气警报", "天气预警", "发布序号", "发布时间", "发布人", "联系电话", "电话", "传真", "盖章"]
    content = "\n".join(ln for ln in lines if not any(k in ln for k in blacklist)).strip()
    icao_match = re.search(r'C_([A-Z]{4})_', file_path.name.upper())
    icao = icao_match.group(1) if icao_match else None
    name = ICAO_NAME.get(icao, icao) if icao else file_path.stem
    return {"name": name, "content": content}

# ---------- 过滤逻辑（来自 fcst0723） ----------
def filter_mdrs_expired(items):
    """
    剔除已过期 MDRS 条目（机场/终端区/航路）。
    规则同 fcst0723。
    """
    filtered = []
    now = datetime.now()

    def safe_dt(y, m, d, h, minute=0):
        try:
            return datetime(y, m, d, h, minute)
        except ValueError:
            return None

    pattern = re.compile(
        r'(?:\d{1,2}月)?'
        r'(?P<ds>\d{1,2})日'
        r'(?P<hs>\d{1,2})(?::?(?P<ms>\d{2}))?(?:时|分|点)?'
        r'[-—~至到]'
        r'(?:\d{1,2}月)?'
        r'(?:(?P<de>\d{1,2})日)?'
        r'(?P<he>\d{1,2})(?::?(?P<me>\d{2}))?(?:时|分|点)?'
    )

    for item in items:
        content = item.get('content', '')
        m = pattern.search(content)
        if not m:
            filtered.append(item)
            continue

        ds = int(m.group('ds'))
        hs = int(m.group('hs'))
        ms = int(m.group('ms')) if m.group('ms') else 0
        de = int(m.group('de')) if m.group('de') else ds
        he = int(m.group('he'))
        me = int(m.group('me')) if m.group('me') else 0

        start = safe_dt(now.year, now.month, ds, hs, ms)
        end = safe_dt(now.year, now.month, de, he, me)
        if not start or not end:
            filtered.append(item)
            continue
        if end < start:
            end = end.replace(year=now.year + 1, month=now.month, day=de)

        if end <= now:
            continue

        if start <= now:
            new_content = pattern.sub('', content).lstrip('，, ')
            prefix = f"{de}日{he:02d}:{me:02d}前，" if me else f"{de}日{he:02d}时前，"
            item['content'] = prefix + new_content

        filtered.append(item)
    return filtered

def filter_alert_expired(alerts):
    """
    使用 latest_time 提取警报正文中最晚时间，与当前时刻比对。
    无时间 → 保留；最晚时间 > 当前 → 保留；否则过滤。
    """
    now = datetime.now()
    return [a for a in alerts if latest_time(a.get("content", "")) is None or latest_time(a.get("content", "")) > now]

# ---------- 快照 ----------
def load_raw():
    return json.loads(SNAPSHOT.read_text(encoding="utf-8")) if SNAPSHOT.exists() else \
           {"airports": [], "terminal_areas": [], "routes": [], "airport_alerts": [], "terminal_alerts": []}

def save_raw(data):
    SNAPSHOT.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")

def dedup(lst):
    seen = OrderedDict()
    for it in lst:
        seen[it["name"]] = it
    return list(seen.values())

# ---------- diff ----------
def make_diff(old_list, new_list):
    last_map = {it["name"]: it for it in old_list}
    now_map = {it["name"]: it for it in new_list}
    result = {}
    for name, it in now_map.items():
        if name not in last_map:
            result[name] = {"new": True, "del": False}
        else:
            old, new = last_map[name]["content"], it["content"]
            if old == new:
                result[name] = None
            else:
                html = []
                for tag, i1, i2, j1, j2 in difflib.SequenceMatcher(None, old, new).get_opcodes():
                    if tag == "equal":
                        html.append(new[j1:j2])
                    elif tag in ("insert", "replace"):
                        html.append(f'<span style="color:red">{new[j1:j2]}</span>')
                    elif tag == "delete":
                        html.append(f'<span style="color:red;text-decoration:line-through">{old[i1:i2]}</span>')
                result[name] = {"new": False, "del": False, "diff_html": "".join(html)}
    for name in last_map:
        if name not in now_map:
            result[name] = {"new": False, "del": True}
    return result

# ---------- 扫描 ----------
def scan_all_unfiltered():
    raw = {"airports": [], "terminal_areas": [], "routes": [], "airport_alerts": [], "terminal_alerts": []}
    for p in list_docx(ROOT_MDRS):
        mdrs = parse_mdrs(p)
        raw["airports"].extend(mdrs["airports"])
        raw["terminal_areas"].extend(mdrs["terminal_areas"])
        raw["routes"].extend(mdrs["routes"])
    raw["airport_alerts"] = [parse_alert(p) for p in list_docx(ROOT_APT) if parse_alert(p)]
    raw["terminal_alerts"] = [parse_alert(p) for p in list_docx(ROOT_TERM) if parse_alert(p)]
    return raw

# ---------- 主接口 ----------
@app.route('/refresh', methods=['GET'])
def api_refresh():
    try:
        old_raw = load_raw()
        new_raw = scan_all_unfiltered()

        diff_airports = make_diff(old_raw["airports"], new_raw["airports"])
        diff_terminal_areas = make_diff(old_raw["terminal_areas"], new_raw["terminal_areas"])
        diff_routes = make_diff(old_raw["routes"], new_raw["routes"])

        filtered = {
            "airports": dedup(filter_mdrs_expired(new_raw["airports"])),
            "terminal_areas": dedup(filter_mdrs_expired(new_raw["terminal_areas"])),
            "routes": dedup(filter_mdrs_expired(new_raw["routes"])),
            "airport_alerts": dedup(filter_alert_expired(new_raw["airport_alerts"])),
            "terminal_alerts": dedup(filter_alert_expired(new_raw["terminal_alerts"]))
        }

        for k in ("airports", "terminal_areas", "routes"):
            for item in filtered[k]:
                item["_diff"] = locals()[f"diff_{k}"].get(item["name"])

        # 打印差异报告
        print("Differences Report:")
        for k, diffs in {"airports": diff_airports, "terminal_areas": diff_terminal_areas, "routes": diff_routes}.items():
            print(f"\n{k.capitalize()}:")
            for name, info in diffs.items():
                if info:
                    print(f"{name}: {info}")

        save_raw(new_raw)
        return jsonify(filtered)
    except Exception as e:
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

# ---------- 启动 ----------
if __name__ == '__main__':
    if not SNAPSHOT.exists():
        save_raw(scan_all_unfiltered())
    app.run(debug=True, port=5001)