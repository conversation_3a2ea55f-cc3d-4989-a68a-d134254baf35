import os, json, re, glob
from flask import Flask, request, jsonify
from flask_cors import CORS
from pathlib import Path
from datetime import datetime

app = Flask(__name__)
CORS(app)

SIGWX_DIR = Path(r"示例\示例\SASP")

REGION_KEYWORDS = {
    '华北': [
        '北京', '首都', '大兴', '天津', '石家庄', '太原', '呼和浩特', '海拉尔',
        'ZBAA', 'ZBAD', 'ZBTJ', 'ZBHH', 'ZBLA',
        'ZUTR', 'ZHJZ', 'ZLJQ'
    ],
    '东北': ['沈阳', '大连', '长春', '哈尔滨', 'ZYTL', 'ZYTX', 'ZYHB', 'ZYCC'],
    '华东': [
        '上海', '虹桥', '浦东', '南京', '杭州', '宁波', '温州', '合肥', '福州', '厦门', '南昌', '济南', '青岛', '烟台', '威海',
        'ZSSS', 'ZSPD', 'ZSNJ', 'ZSWZ', 'ZSHC', 'ZSNB', 'ZSAM', 'ZSFZ', 'ZSQD', 'ZSYT', 'ZSWF', 'ZSJN'
    ],
    '中南': [
        '郑州', '武汉', '长沙', '广州', '深圳', '珠海', '汕头', '湛江', '南宁', '桂林', '海口', '三亚',
        'ZGGG', 'ZGSZ', 'ZHCC', 'ZHHH', 'ZGHA', 'ZGSY', 'ZGNN', 'ZGKL', 'ZGHY', 'ZGHK', 'ZJSY', 'ZGOW', 'ZJYX'
    ],
    '西南': [
        '重庆', '成都', '双流', '天府', '贵阳', '昆明', '拉萨',
        'ZUUU', 'ZUCK', 'ZPPP', 'ZUTF', 'ZULS', 'ZUXC', 'ZUNZ', 'ZUBD', 'ZUMY', 'ZPLC', 'ZPDQ', 'ZULZ'
    ],
    '西北': [
        '西安', '兰州', '西宁', '银川',
        'ZLXY', 'ZLLL', 'ZLHW', 'ZLYA', 'ZLJQ', 'ZLIC', 'ZLGM', 'ZLHZ', 'ZLYL', 'ZLXN'
    ],
    '新疆': [
        '乌鲁木齐', '喀什', '和田', '伊犁', '塔城', '阿勒泰',
        'ZWWW', 'ZWSH', 'ZWKC', 'ZWKL', 'ZWCM', 'ZWAK', 'ZWAT', 'ZWYN', 'ZWKM'
    ]
}

def belongs_to_region(station, region):
    return True   # 所有站都放行

def format_sigwx_line(station: str, content: str, o_date_time: str) -> str:
    """
    返回形如：
    ZBAA: 260049Z 36015G20MPS FG +RA TSRA CB
    """
    txt = content.upper()

    # 1. 直接从 CONTENT 里提取 DDHHMMZ
    raw_time = ""
    m = re.search(r'\b(\d{6}Z)\b', txt)
    if m:
        raw_time = m.group(1)          # 例如 260049Z
    if not raw_time:
        return None                    # 没有时间戳就不输出

    parts = [raw_time]

    # 2. 风速≥12MPS
    wind_match = re.search(r'\b\d{3}(\d{2}(?:G\d{2})?)MPS\b', txt)
    if wind_match and int(wind_match.group(1)[:2]) >= 12:
        parts.append(wind_match.group(0))

    # 3. 低能见度关键词
    vis_keywords = ['FG', 'MIFG', 'FZFG', 'SS', 'DS']
    for kw in vis_keywords:
        if re.search(rf'\b{kw}\b', txt):
            parts.append(kw)

    # 4. 对流和降水
    conv_patterns = [
        r'\+TSRA\b', r'\bTS\b', r'\bSQ\b', r'\bFC\b', r'\bGR\b', r'\bRA\b',
        r'\bSHRA\b', r'\b\+RA\b', r'\bSN\b', r'\-TSRA\b'
    ]
    for pat in conv_patterns:
        m = re.search(pat, txt)
        if m:
            parts.append(m.group(0))

    # 5. 强制保留 CB
    if re.search(r'\bCB\b', txt):
        parts.append('CB')

    # 6. 去重
    seen = set()
    filtered = [raw_time] + [p for p in parts[1:] if not (p in seen or seen.add(p))]

    # 7. 只有时间戳就不输出
    if len(filtered) == 1:
        return None

    return f"{station}: {' '.join(filtered)}".strip()

@app.route("/get_sigwx")
def get_sigwx():
    region = request.args.get("region", "华北")
    files = glob.glob(str(SIGWX_DIR / "*.txt"))
    results = {}

    for fp in sorted(files, key=os.path.getmtime, reverse=True):
        try:
            with open(fp, encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    j = json.loads(line)
                    station = j.get("OBCC", "")
                    content = j.get("CONTENT", "").strip()
                    if not content:
                        continue
                    if not belongs_to_region(station, region):
                        continue
                    # 格式化重要天气行
                    raw_line = format_sigwx_line(station, content, j.get("O_DATE_TIME", ""))
                    if raw_line:
                        results[station] = {
                            "name": station,
                            "raw": raw_line
                        }
        except Exception as e:
            print(f"Error processing file {fp}: {e}")
            continue

    return jsonify(results)

if __name__ == "__main__":
    app.run(port=5002, debug=True)