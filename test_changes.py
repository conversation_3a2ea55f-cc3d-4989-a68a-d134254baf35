# -*- coding: utf-8 -*-
"""
测试数据变化的脚本
用于模拟天气数据的变化，以测试绿点提示和差异化展示功能
"""

import json
import time
from pathlib import Path

# 模拟数据
test_data_1 = {
    "airports": [
        {
            "name": "北京首都机场",
            "content": "2日06:30-11:00，云底高6-10千米，周围11千米雷雨，周围伴风切变、大风",
            "remark": "",
            "gray_bg": False
        },
        {
            "name": "浦东机场", 
            "content": "无",
            "remark": "",
            "gray_bg": False
        }
    ],
    "terminal_areas": [
        {
            "name": "北京首都机场",
            "content": "2日06:30-11:00，云底高6-10千米，周围11千米雷雨，周围伴风切变、大风",
            "remark": "",
            "gray_bg": False
        },
        {
            "name": "浦东机场",
            "content": "2日06:10-09:00，中雨雨伴雷暴",
            "remark": "",
            "gray_bg": False
        }
    ],
    "routes": [
        {
            "name": "温州龙湾航路",
            "content": "2日18-21时，云顶高7-11千米雷雨伴雷暴",
            "remark": ""
        }
    ],
    "airport_alerts": [],
    "terminal_alerts": []
}

test_data_2 = {
    "airports": [
        {
            "name": "北京首都机场",
            "content": "2日06:30-11:00，云底高6-10千米，周围11千米雷雨，周围伴风切变、大风、冰雹",  # 新增"冰雹"
            "remark": "",
            "gray_bg": False
        },
        {
            "name": "浦东机场", 
            "content": "2日08:00-12:00，中雨雨伴雷暴",  # 从"无"变为有内容
            "remark": "",
            "gray_bg": False
        }
    ],
    "terminal_areas": [
        {
            "name": "北京首都机场",
            "content": "无",  # 从有内容变为"无"
            "remark": "",
            "gray_bg": False
        },
        {
            "name": "浦东机场",
            "content": "2日06:10-09:00，中雨雨伴雷暴、大风",  # 新增"大风"
            "remark": "",
            "gray_bg": False
        }
    ],
    "routes": [
        {
            "name": "温州龙湾航路",
            "content": "2日18-21时，云顶高7-11千米雷雨伴雷暴、冰雹",  # 新增"冰雹"
            "remark": ""
        },
        {
            "name": "厦门翔安航路",  # 新增航路
            "content": "3日05-10时，中雨伴雷暴",
            "remark": ""
        }
    ],
    "airport_alerts": [],
    "terminal_alerts": []
}

def create_mock_api():
    """创建模拟API响应"""
    from flask import Flask, jsonify
    from flask_cors import CORS
    
    app = Flask(__name__)
    CORS(app, resources={r"/*": {"origins": "*"}})
    
    # 全局变量控制返回哪个数据集
    current_data = test_data_1
    
    @app.route('/refresh', methods=['GET'])
    def refresh():
        return jsonify(current_data)
    
    @app.route('/switch_data', methods=['POST'])
    def switch_data():
        global current_data
        if current_data == test_data_1:
            current_data = test_data_2
            return jsonify({"message": "Switched to test_data_2", "status": "success"})
        else:
            current_data = test_data_1
            return jsonify({"message": "Switched to test_data_1", "status": "success"})
    
    return app

if __name__ == '__main__':
    print("启动测试API服务器...")
    print("使用方法：")
    print("1. 在浏览器中打开天气查询页面")
    print("2. 点击'刷新数据'查看初始数据")
    print("3. 访问 http://localhost:5001/switch_data 切换数据")
    print("4. 再次点击'刷新数据'查看变化效果")
    print("5. 观察绿点提示和红色文字/删除线效果")
    
    app = create_mock_api()
    app.run(debug=True, port=5001)
