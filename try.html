<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>实时天气查询系统</title>
  <style>
    /* ===== 基础样式 ===== */
    body{font-family:"Microsoft YaHei",sans-serif;margin:0;padding:20px;background:#f5f7fa;color:#333}
    .container{max-width:1200px;margin:auto;box-shadow:0 0 20px rgba(0,0,0,.1);border-radius:10px;overflow:hidden;background:#fff}
    header{background:linear-gradient(135deg,#1e5799,#207cca);color:#fff;padding:20px;text-align:center;position:relative}
    .region-selector{margin:20px 0;text-align:center}
    .region-selector button{margin:4px;padding:8px 14px;border:none;border-radius:4px;background:#2c3e50;color:#fff;cursor:pointer;transition:all .3s;position:relative}
    .region-selector button:hover{background:#34495e;transform:translateY(-2px)}
    .region-selector button.active{background:#e74c3c;box-shadow:0 2px 5px rgba(0,0,0,.2)}
    table{width:100%;border-collapse:collapse;background:#fff;border-radius:8px;overflow:hidden;box-shadow:0 2px 8px rgba(0,0,0,.1)}
    th,td{padding:10px 12px;border:1px solid #ddd;text-align:center;font-size:14px}
    th{background:#2c3e50;color:#fff;font-weight:600}
    .gray-bg{background:#f0f0f0}
    .red-txt{background:#f0f0f0;color:#e74c3c;border-radius:3px;padding:2px 4px;font-size:12px}
    .remark{color:#7f8c8d;font-size:12px;margin-top:4px}
    .alert-row{background:#fff0f0;color:#333}
    .alert-row td{text-align:left;padding-left:10px}
    .no-data{padding:30px;text-align:center;color:#999}
    .route-list{margin:0;padding-left:20px}
    .route-list li{margin-bottom:5px}
    .dot{display:inline-block;width:8px;height:8px;background:#2ecc71;border-radius:50%;margin-right:6px;vertical-align:middle}
    .region-dot{position:absolute;left:5px;top:50%;transform:translateY(-50%);width:6px;height:6px;background:#2ecc71;border-radius:50%}
    #btnRefresh{position:absolute;right:20px;top:20px;background:#27ae60;border:none;color:#fff;padding:6px 14px;border-radius:4px;cursor:pointer;font-weight:bold}
    #btnRefresh:hover{background:#2ecc71;transform:translateY(-2px)}
    #btnRefresh:disabled{background:#aaa;cursor:not-allowed}
    #sigwx-banner{margin:10px 20px;font-size:14px;color:#e74c3c;line-height:1.8;padding:10px;background:#fff8f8;border-radius:8px;border-left:4px solid #e74c3c;box-shadow:0 1px 3px rgba(0,0,0,.1)}
    .export-controls{margin:10px 20px;text-align:center}
    .export-btn{margin:0 5px;padding:10px 16px;background:#3498db;color:#fff;border:none;border-radius:4px;cursor:pointer;width:120px;text-align:center}
    .export-btn:hover{background:#2980b9;transform:translateY(-2px);box-shadow:0 2px 5px rgba(0,0,0,.2)}
    .row-checkbox{margin-right:5px;cursor:pointer}
    .select-all-row{margin-right:5px}
    #global-loader,#ppt-loader{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.6);color:#fff;display:none;justify-content:center;align-items:center;z-index:9999;font-size:20px}
    .loader-spinner{border:5px solid #f3f3f3;border-top:5px solid #3498db;border-radius:50%;width:50px;height:50px;animation:spin 1s linear infinite}
    @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
    #weather-cancel{margin:10px 20px;padding:8px 12px;background:#fff6f6;border-left:4px solid #e74c3c;color:#e74c3c;font-size:14px;border-radius:4px;display:none}
.red-text {
  color: red;
}
.del-line {
  text-decoration: line-through;
  color: red;
}
.row-change-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #2ecc71;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}
 </style>
</head>
<body>
  <div id="global-loader">数据刷新中...</div>
  <div id="ppt-loader"><div class="loader-spinner"></div><div style="margin-top:20px;font-size:18px">正在生成PPT，请稍候...</div></div>
  <div id="weather-cancel"></div>
  <div class="container">
    <header>
      <h1>实时天气查询系统</h1>
      <div class="subtitle" id="subtitle">华北地区</div>
      <div style="position:absolute;right:20px;top:20px;text-align:center;line-height:1">
        <button id="btnRefresh" style="white-space:nowrap">刷新数据</button>
        <div style="margin-top:6px;font-size:12px;color:#fff;white-space:nowrap"><span class="dot" style="margin-right:4px"></span>表示内容更新</div>
      </div>
    </header>
    <div id="sigwx-banner"><div id="sigwx-content">正在读取重要天气信息...</div></div>
    <div class="region-selector">
      <button data-region="华北" class="active">华北</button>
      <button data-region="东北">东北</button>
      <button data-region="华东">华东</button>
      <button data-region="中南">中南</button>
      <button data-region="西南">西南</button>
      <button data-region="西北">西北</button>
      <button data-region="新疆">新疆</button>
    </div>
    <div class="export-controls" style="position:fixed;right:20px;bottom:20px;z-index:10;display:flex;flex-direction:column;gap:10px">
      <button class="export-btn" onclick="exportToPPT('all')">导出全部</button>
      <button class="export-btn" onclick="exportToPPT('region')">导出当前地区</button>
      <button class="export-btn" onclick="exportToPPT('selected')">导出指定行</button>
    </div>
    <table>
      <thead>
        <tr>
          <th width="5%"><input type="checkbox" id="selectAll_${CURRENT_REGION}" onchange="toggleSelectAll(this)"></th>
          <th width="7%">城市</th>
          <th width="35%">机场预报</th>
          <th width="30%">终端区预报</th>
          <th width="23%">航路天气</th>
        </tr>
      </thead>
      <tbody id="weather-tbody"><tr><td colspan="5" class="no-data">加载天气数据中...</td></tr></tbody>
    </table>
    <footer>气象数据更新时间：<span id="update-time">--:--:--</span> | MDRS：<span class="red-txt">白底</span> | 机场或终端区警报：<span class="red-txt">红底</span></footer>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/pptxgenjs@3.12.0/dist/pptxgen.bundle.js"></script>
  <script>
    if (performance.navigation.type === 1 || !sessionStorage.getItem('loaded')) {
      localStorage.removeItem('wx_selected_global');
      ['selectAll_华北','selectAll_东北','selectAll_华东','selectAll_中南','selectAll_西南','selectAll_西北','selectAll_新疆']
        .forEach(k => localStorage.removeItem(k));
      sessionStorage.setItem('loaded','1');
    }
    if (!sessionStorage.getItem('wx_first')) {
      localStorage.removeItem('wx_selected_global');
      sessionStorage.setItem('wx_first', '1');
    }
    const REGION_KEYWORDS = {
      华北: ['北京','首都','大兴','天津','石家庄','太原','呼和浩特','海拉尔','ZBAA','ZBAD','ZBTJ','ZBSJ','ZBYN','ZBHH','ZBLA'],
      东北: ['沈阳','大连','长春','哈尔滨','ZYTX','ZYTL','ZYCC','ZYHB'],
      华东: ['上海','虹桥','浦东','南京','杭州','宁波','温州','合肥','福州','厦门','南昌','济南','青岛','烟台','威海','ZSSS','ZSPD','ZSNJ','ZSHC','ZSNB','ZSWZ','ZSOF','ZSFZ','ZSAM','ZSCN','ZSJN','ZSQD','ZSYT','ZSWF'],
      中南: ['郑州','武汉','长沙','广州','深圳','珠海','汕头','湛江','南宁','桂林','海口','三亚','ZHCC','ZHHH','ZGHA','ZGGG','ZGSZ','ZGSD','ZGOW','ZGZJ','ZGNN','ZGKL','ZJHK','ZJSY'],
      西南: ['重庆','成都','双流','天府','贵阳','昆明','拉萨','ZUCK','ZUUU','ZUTF','ZUGY','ZPPP','ZULS'],
      西北: ['西安','兰州','西宁','银川','ZLXY','ZLLL','ZLXN','ZLIC'],
      新疆: ['乌鲁木齐','喀什','和田','伊犁','塔城','阿勒泰','ZWWW','ZWSH','ZWTN','ZWYN','ZWTC','ZWAT']
    };
    const REGIONS = Object.keys(REGION_KEYWORDS);
    function formatTime(d){return d.toTimeString().split(' ')[0];}
    function getDisplayName(raw){
      if(/首都|ZBAA/i.test(raw)) return '首都';
      if(/大兴|ZBAD/i.test(raw)) return '大兴';
      if(/虹桥|ZSSS/i.test(raw)) return '虹桥';
      if(/浦东|ZSPD/i.test(raw)) return '浦东';
      if(/双流|ZUUU/i.test(raw)) return '双流';
      if(/天府|ZUTF/i.test(raw)) return '天府';
      if(/北京|首都/i.test(raw)) return '首都';
      if(/成都|双流/i.test(raw)) return '双流';
      return raw.replace(/[A-Z]{4}/g,'').replace(/机场|终端区|\(.*?\)/g,'')
        .replace(/武宿|宝安|长水|金湾|白云|滨海|江北|咸阳|天河|禄口|萧山|黄花|新郑|胶东|桃仙|周水子|高崎|长乐|遥墙|太平|龙嘉|正定|中川|地窝堡|天山|美兰|凤凰|吴圩|龙洞堡|贡嘎|昌北|新桥|栎社|龙湾|白塔|河东|曹家堡/g,'')
        .trim().match(/^[\u4e00-\u9fa5]{2,}/)?.[0] || raw.slice(0,4);
    }
    function filterByRegion(arr, region = CURRENT_REGION){
      const keys = REGION_KEYWORDS[region] || [];
      return arr.filter(it => keys.some(k => it.name.includes(k)));
    }
    let REGION_UPDATE_FLAGS = {};
    function saveFullSnapshot(data){ localStorage.setItem('wx_full_snapshot', JSON.stringify(data)); }
    function loadFullSnapshot(){ const s = localStorage.getItem('wx_full_snapshot'); return s ? JSON.parse(s) : null; }
    let lastData = null;
    function isRegionDataSame(oldData, newData, region) {
      const keys = REGION_KEYWORDS[region] || [];
      const filterByKeyword = (arr) => (arr || [])
        .filter(it => keys.some(k => it.name.includes(k)));
      const normalize = arr => (arr || [])
        .map(i => ({ name: (i.name || '').trim(), content: (i.content || '').trim().replace(/\s+/g, ' '), remark: (i.remark || '').trim().replace(/\s+/g, ' ') }))
        .sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'));
      const same = (a, b) => JSON.stringify(normalize(a)) === JSON.stringify(normalize(b));
      return (
        same(filterByKeyword(oldData.airports), filterByKeyword(newData.airports)) &&
        same(filterByKeyword(oldData.terminal_areas), filterByKeyword(newData.terminal_areas)) &&
        same(filterByKeyword(oldData.routes), filterByKeyword(newData.routes)) &&
        same(filterByKeyword(oldData.airport_alerts), filterByKeyword(newData.airport_alerts)) &&
        same(filterByKeyword(oldData.terminal_alerts), filterByKeyword(newData.terminal_alerts))
      );
    }
    function refreshRegionUpdateFlags() {
      REGION_UPDATE_FLAGS = {};
      if (!lastData) {
        try {
          const initialSnapshot = JSON.parse(localStorage.getItem('wx_initial_snapshot') || '{"airports":[],"terminal_areas":[],"routes":[],"airport_alerts":[],"terminal_alerts":[]}');
          REGIONS.forEach(r => { REGION_UPDATE_FLAGS[r] = !isRegionDataSame(initialSnapshot, ALL_DATA, r); });
        } catch {
          REGIONS.forEach(r => { REGION_UPDATE_FLAGS[r] = true; });
        }
      } else {
        REGIONS.forEach(r => { REGION_UPDATE_FLAGS[r] = !isRegionDataSame(lastData, ALL_DATA, r); });
      }
      lastData = {
        airports: [...ALL_DATA.airports.map(item => ({...item}))],
        terminal_areas: [...ALL_DATA.terminal_areas.map(item => ({...item}))],
        routes: [...ALL_DATA.routes.map(item => ({...item}))],
        airport_alerts: [...ALL_DATA.airport_alerts.map(item => ({...item}))],
        terminal_alerts: [...ALL_DATA.terminal_alerts.map(item => ({...item}))]
      };
    }
    function hasRegionUpdate(region){ return !!REGION_UPDATE_FLAGS[region]; }
    function showGlobalLoader(){document.getElementById('global-loader').style.display='flex';}
    function hideGlobalLoader(){document.getElementById('global-loader').style.display='none';}
    function loadData(manual = false) {
      if (manual !== false) {
        localStorage.removeItem('wx_selected_global');
        REGIONS.forEach(region => {
          localStorage.removeItem(`selectAll_${region}`);
        });
        document.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = false);
        document.querySelectorAll('thead input[type="checkbox"]').forEach(cb => cb.checked = false);
      }
      showGlobalLoader();

      // 在手动刷新时，保存当前数据作为比较基准
      if (manual && ALL_DATA) {
        lastData = {
          airports: [...(ALL_DATA.airports || []).map(item => ({...item}))],
          terminal_areas: [...(ALL_DATA.terminal_areas || []).map(item => ({...item}))],
          routes: [...(ALL_DATA.routes || []).map(item => ({...item}))],
          airport_alerts: [...(ALL_DATA.airport_alerts || []).map(item => ({...item}))],
          terminal_alerts: [...(ALL_DATA.terminal_alerts || []).map(item => ({...item}))]
        };
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000);
      fetch('http://localhost:5001/refresh', { signal: controller.signal })
        .then(r => r.json())
        .then(data => {
          ALL_DATA = {
            airports: data.airports || [],
            terminal_areas: data.terminal_areas || [],
            routes: data.routes || [],
            airport_alerts: data.airport_alerts || [],
            terminal_alerts: data.terminal_alerts || []
          };
          if (manual) {
            refreshRegionUpdateFlags();
          }
          render();
          loadSigWx();
          document.getElementById('update-time').textContent = formatTime(new Date());
        })
        .catch(err => {
          console.error('加载失败：', err);
          document.getElementById('weather-tbody').innerHTML = '<tr><td colspan="5" class="no-data">加载失败，请重试</td></tr>';
        })
        .finally(hideGlobalLoader);
    }
    function loadSigWx(){
      fetch(`http://localhost:5002/get_sigwx?region=${encodeURIComponent(CURRENT_REGION)}`)
        .then(r=>r.json())
        .then(data=>{
          const list=Object.values(data);
          if(!list.length){document.getElementById('sigwx-content').innerHTML='当前地区暂无重要天气信息';return;}
          document.getElementById('sigwx-content').innerHTML=`<strong style="color:#000">【当前重要天气】</strong><br><span style="color:#000">${list.map(d=>d.raw).join('<br>')}</span>`;
        })
        .catch(()=>document.getElementById('sigwx-content').innerHTML='<span style="color:#e74c3c">重要天气信息加载失败</span>');
    }

    // 检测单行数据是否发生变化
    function hasRowChanged(displayName, currentAirport, currentTerminal, currentRoutes) {
      if (!lastData) return false;

      // 检查机场预报变化
      const oldAirport = lastData.airports?.find(a => getDisplayName(a.name) === displayName);
      const airportChanged = checkContentChanged(oldAirport, currentAirport);

      // 检查终端区预报变化
      const oldTerminal = lastData.terminal_areas?.find(t => getDisplayName(t.name) === displayName);
      const terminalChanged = checkContentChanged(oldTerminal, currentTerminal);

      // 检查航路天气变化（需要检查所有相关的航路）
      const routeChanged = checkRouteChanged(displayName, currentRoutes);

      return airportChanged || terminalChanged || routeChanged;
    }

    // 检测内容变化的辅助函数
    function checkContentChanged(oldItem, newItem) {
      // 情况1：从无到有
      if (!oldItem && newItem && !newItem.content.includes('，无')) {
        return true;
      }

      // 情况2：从有到无
      if (oldItem && (!newItem || newItem.content.includes('，无'))) {
        return true;
      }

      // 情况3：内容发生变化
      if (oldItem && newItem && !newItem.content.includes('，无')) {
        const oldContent = (oldItem.content || '').trim().replace(/\s+/g, ' ');
        const newContent = (newItem.content || '').trim().replace(/\s+/g, ' ');
        return oldContent !== newContent;
      }

      return false;
    }

    // 检测航路天气变化
    function checkRouteChanged(displayName, currentRoutes) {
      if (!lastData || !lastData.routes) return false;

      // 获取当前区域的关键词
      const keys = REGION_KEYWORDS[CURRENT_REGION] || [];

      // 过滤出相关的航路数据
      const oldRoutes = lastData.routes.filter(r => keys.some(k => r.name.includes(k)));
      const newRoutes = currentRoutes.filter(r => keys.some(k => r.name.includes(k)));

      // 比较航路数据
      if (oldRoutes.length !== newRoutes.length) return true;

      for (let i = 0; i < oldRoutes.length; i++) {
        const oldRoute = oldRoutes[i];
        const newRoute = newRoutes.find(r => r.name === oldRoute.name);
        if (!newRoute) return true;

        const oldContent = (oldRoute.content || '').trim().replace(/\s+/g, ' ');
        const newContent = (newRoute.content || '').trim().replace(/\s+/g, ' ');
        if (oldContent !== newContent) return true;
      }

      return false;
    }

    // 新增：处理差异内容的函数
    function render() {
  const selectedGlobal = JSON.parse(localStorage.getItem('wx_selected_global') || '{}');
  const selectedNow = new Set(Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => cb.dataset.row));
  const merged = { ...selectedGlobal };
  document.querySelectorAll('.row-checkbox').forEach(cb => {
    const key = cb.dataset.row;
    if (selectedNow.has(key)) merged[key] = cb.dataset.region;
    else if (!merged[key]) delete merged[key];
  });
  localStorage.setItem('wx_selected_global', JSON.stringify(merged));

  const airports = filterByRegion(ALL_DATA.airports || []);
  const terminals = filterByRegion(ALL_DATA.terminal_areas || []);
  const routes = filterByRegion(ALL_DATA.routes || []);
  const airportAlerts = filterByRegion(ALL_DATA.airport_alerts || []);
  const terminalAlerts = filterByRegion(ALL_DATA.terminal_alerts || []);

  document.getElementById('subtitle').textContent = CURRENT_REGION + '地区';
  const tbody = document.getElementById('weather-tbody');
  tbody.innerHTML = '';

  const allId = `selectAll_${CURRENT_REGION}`;
  const savedAll = JSON.parse(localStorage.getItem(allId) || 'false');
  const thAll = document.querySelector('thead tr th:first-child');
  thAll.innerHTML = `<input type="checkbox" id="${allId}" ${savedAll ? 'checked' : ''}>`;
  const cbAll = document.getElementById(allId);
  cbAll.onchange = () => {
    const isChecked = cbAll.checked;
    localStorage.setItem(allId, isChecked);
    const global = JSON.parse(localStorage.getItem('wx_selected_global') || '{}');
    document.querySelectorAll('.row-checkbox').forEach(cb => {
      if (cb.dataset.region === CURRENT_REGION) {
        cb.checked = isChecked;
        const key = cb.dataset.row;
        if (isChecked) global[key] = CURRENT_REGION;
        else delete global[key];
      }
    });
    localStorage.setItem('wx_selected_global', JSON.stringify(global));
  };

  const br = txt => (txt || '').replace(/\n/g, '<br>');

  const cityMap = new Map();
  [...airports, ...terminals].forEach(item => {
    const key = getDisplayName(item.name);
    if (!cityMap.has(key)) cityMap.set(key, { displayName: key, airport: null, terminal: null });
    if (airports.some(a => getDisplayName(a.name) === key)) cityMap.get(key).airport = item;
    if (terminals.some(t => getDisplayName(t.name) === key)) cityMap.get(key).terminal = item;
  });
  [...airportAlerts, ...terminalAlerts].forEach(alert => {
    const key = getDisplayName(alert.name);
    if (!cityMap.has(key)) cityMap.set(key, { displayName: key, airport: null, terminal: null });
  });

  const REGION_ORDER = {
    华北: ['首都', '大兴', '天津', '石家庄', '太原', '呼和浩特', '海拉尔'],
    东北: ['哈尔滨', '长春', '沈阳', '大连'],
    华东: ['虹桥', '浦东', '南京', '杭州', '宁波', '温州', '合肥', '福州', '厦门', '南昌', '济南', '青岛', '烟台', '威海'],
    中南: ['郑州', '武汉', '长沙', '广州', '深圳', '珠海', '汕头', '湛江', '南宁', '桂林', '海口', '三亚'],
    西南: ['双流', '天府', '重庆', '贵阳', '昆明', '拉萨'],
    西北: ['西安', '兰州', '西宁', '银川'],
    新疆: ['乌鲁木齐', '喀什', '和田', '伊犁', '塔城', '阿勒泰']
  };

  const order = REGION_ORDER[CURRENT_REGION] || [];
  const rows = [...cityMap.values()]
    .filter(row => {
      const hasAirportContent = row.airport && row.airport.content && !row.airport.content.includes('，无');
      const hasTerminalContent = row.terminal && row.terminal.content && !row.terminal.content.includes('，无');
      const hasAlert =
        airportAlerts.some(a => getDisplayName(a.name) === row.displayName) ||
        terminalAlerts.some(t => getDisplayName(t.name) === row.displayName);

      // 判断是否“从有到无”
      const oldAirport = lastData?.airports?.find(a => getDisplayName(a.name) === row.displayName);
      const oldTerminal = lastData?.terminal_areas?.find(t => getDisplayName(t.name) === row.displayName);
      const airportDeleted = oldAirport && (!row.airport || row.airport.content.includes('，无'));
      const terminalDeleted = oldTerminal && (!row.terminal || row.terminal.content.includes('，无'));

      return hasAirportContent || hasTerminalContent || hasAlert || airportDeleted || terminalDeleted;
    })
    .sort((a, b) => {
      const indexA = order.indexOf(a.displayName);
      const indexB = order.indexOf(b.displayName);
      if (indexA === -1 && indexB === -1) return a.displayName.localeCompare(b.displayName, 'zh-CN');
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    });

  if (rows.length === 0 && routes.length === 0) {
    tbody.innerHTML = '<tr><td colspan="5" class="no-data">当前区域暂无天气数据</td></tr>';
    return;
  }

  const frag = document.createDocumentFragment();
  const currentRegionHasUpdate = hasRegionUpdate(CURRENT_REGION);

  // 处理航路天气差异样式
  const processedRoutes = routes.map(route => {
    let content = br(route.content).replace(/！/g, '');
    let routeClass = '';

    // 检查航路天气是否发生变化
    if (lastData && lastData.routes) {
      const oldRoute = lastData.routes.find(r => r.name === route.name);
      if (checkContentChanged(oldRoute, route)) {
        routeClass = 'red-text';
      }
      // 检查是否为删除的航路（旧数据中有，新数据中没有）
      else if (oldRoute && (!route || route.content.includes('，无'))) {
        content = br(oldRoute.content).replace(/！/g, '');
        routeClass = 'del-line';
      }
    }

    return {
      ...route,
      content: `<span class="${routeClass}">${content}</span>`
    };
  });

  // 检查是否有被删除的航路（在旧数据中存在但新数据中不存在）
  if (lastData && lastData.routes) {
    const keys = REGION_KEYWORDS[CURRENT_REGION] || [];
    const oldRoutes = lastData.routes.filter(r => keys.some(k => r.name.includes(k)));
    const currentRouteNames = routes.map(r => r.name);

    oldRoutes.forEach(oldRoute => {
      if (!currentRouteNames.includes(oldRoute.name)) {
        // 添加被删除的航路
        processedRoutes.push({
          ...oldRoute,
          content: `<span class="del-line">${br(oldRoute.content).replace(/！/g, '')}</span>`
        });
      }
    });
  }

  rows.forEach((row, idx) => {
    const { displayName, airport, terminal } = row;
    const aAlerts = airportAlerts.filter(a => a.name.includes(displayName));
    const tAlerts = terminalAlerts.filter(t => t.name.includes(displayName));

    // 检测当前行是否有变化
    const rowHasChanged = hasRowChanged(displayName, airport, terminal, routes);

    // 判断是否“从有到无”
    const oldAirport = lastData?.airports?.find(a => getDisplayName(a.name) === displayName);
    const oldTerminal = lastData?.terminal_areas?.find(t => getDisplayName(t.name) === displayName);
    const airportDeleted = oldAirport && (!airport || airport.content.includes('，无'));
    const terminalDeleted = oldTerminal && (!terminal || terminal.content.includes('，无'));

    // 处理机场预报内容和样式
    let airportContent = '';
    let airportClass = '';
    if (airportDeleted) {
      // 取消的预报：显示旧内容并加删除线
      airportContent = br(oldAirport.content);
      airportClass = 'del-line';
    } else if (airport && !airport.content.includes('，无')) {
      airportContent = br(airport.content).replace(/！/g, '');
      // 检查是否为新增或变化的内容
      if (checkContentChanged(oldAirport, airport)) {
        airportClass = 'red-text';
      }
    }

    // 处理终端区预报内容和样式
    let terminalContent = '';
    let terminalClass = '';
    if (terminalDeleted) {
      // 取消的预报：显示旧内容并加删除线
      terminalContent = br(oldTerminal.content);
      terminalClass = 'del-line';
    } else if (terminal && !terminal.content.includes('，无')) {
      terminalContent = br(terminal.content).replace(/！/g, '');
      // 检查是否为新增或变化的内容
      if (checkContentChanged(oldTerminal, terminal)) {
        terminalClass = 'red-text';
      }
    }

    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td><input type="checkbox" class="row-checkbox" data-row="${displayName}" data-region="${CURRENT_REGION}" ${merged[displayName] ? 'checked' : ''}></td>
      <td>${rowHasChanged ? '<span class="row-change-indicator"></span>' : ''}${displayName}</td>
      <td ${airport && !airportDeleted && airport.gray_bg ? 'class="gray-bg"' : ''}>
        ${airportContent ? `<span class="${airportClass}">${airportContent}</span>` : ''}
        ${airport && airport.remark ? `<div class="remark">${br(airport.remark)}</div>` : ''}
      </td>
      <td ${terminal && !terminalDeleted && terminal.gray_bg ? 'class="gray-bg"' : ''}>
        ${terminalContent ? `<span class="${terminalClass}">${terminalContent}</span>` : ''}
        ${terminal && terminal.remark ? `<div class="remark">${br(terminal.remark)}</div>` : ''}
      </td>
      ${idx === 0 && processedRoutes.length ? `<td rowspan="${rows.length}" style="vertical-align:top;text-align:left;padding:10px 15px">
        <div style="display:inline-flex;align-items:center"><strong>航路天气</strong></div>
        <ul class="route-list">${processedRoutes.map(r => `<li><strong>${r.name}</strong>：${r.content}${r.remark ? '<br>' + br(r.remark) : ''}</li>`).join('')}</ul>
      </td>` : ''}`;

    const cb = tr.querySelector('.row-checkbox');
    cb.onchange = () => {
      const key = cb.dataset.row;
      const global = JSON.parse(localStorage.getItem('wx_selected_global') || '{}');
      if (!cb.checked) delete global[key]; else global[key] = CURRENT_REGION;
      localStorage.setItem('wx_selected_global', JSON.stringify(global));
    };
    frag.appendChild(tr);

    // 警报行（无绿点）
    const maxLen = Math.max(aAlerts.length, tAlerts.length);
    for (let i = 0; i < maxLen; i++) {
      const trAlert = document.createElement('tr');
      trAlert.className = 'alert-row';
      trAlert.innerHTML = `
        <td></td><td></td>
        <td>${aAlerts[i] ? `<span style="background:#e74c3c;color:#fff;padding:2px 4px;border-radius:3px">机场警报</span> ${br(aAlerts[i].content)}` : ''}</td>
        <td>${tAlerts[i] ? `<span style="background:#e74c3c;color:#fff;padding:2px 4px;border-radius:3px">终端区警报</span> ${br(tAlerts[i].content)}` : ''}</td>
        <td></td>`;
      frag.appendChild(trAlert);
    }
  });

  if (rows.length === 0 && processedRoutes.length) {
    const tr = document.createElement('tr');
    tr.innerHTML = `<td colspan="5" style="text-align:left;padding:10px 15px">
      <div style="display:inline-flex;align-items:center">${currentRegionHasUpdate && processedRoutes.length ? '<span class="dot" title="航路天气更新"></span>' : ''}<strong>航路重要天气</strong></div>
      <ul class="route-list">${processedRoutes.map(r => `<li><strong>${r.name}</strong>：${r.content}${r.remark ? '<br>' + br(r.remark) : ''}</li>`).join('')}</ul>
    </td>`;
    frag.appendChild(tr);
  }

  tbody.appendChild(frag);
  document.querySelectorAll('.region-selector button').forEach(btn => {
    const region = btn.dataset.region;
    let dot = btn.querySelector('.region-dot');
    if (!dot) { dot = document.createElement('span'); dot.className = 'region-dot'; btn.prepend(dot); }
    dot.style.display = hasRegionUpdate(region) ? 'inline-block' : 'none';
  });
}


    
    function toggleSelectAll(cb){ document.querySelectorAll('.row-checkbox').forEach(ch=>ch.checked=cb.checked); }
    function exportToPPT(mode) {
      const loader = document.getElementById('ppt-loader');
      loader.style.display = 'flex';
      if (typeof PptxGenJS === 'undefined') {
        setTimeout(() => {
          if (typeof PptxGenJS === 'undefined') {
            loader.style.display = 'none';
            alert('PPT生成库加载失败，请刷新页面后重试');
            return;
          }
          generatePPT(mode, loader);
        }, 1000);
      } else {
        generatePPT(mode, loader);
      }
    }
    function generatePPT(mode, loader) {
      try {
        const pptx = new PptxGenJS();
        const now = new Date();
        const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}时${now.getMinutes()}分`;
        let title, slidesData = [];
        if (mode === 'all') {
          title = `全国天气预报 - ${timeStr}`;
          REGIONS.forEach(region => {
            const data = getFullRegionData(region);
            if (data.rows.length > 0) slidesData.push(data);
          });
        } else if (mode === 'region') {
          title = `${CURRENT_REGION}地区天气预报 - ${timeStr}`;
          slidesData.push(getFullRegionData(CURRENT_REGION));
        } else if (mode === 'selected') {
          const selectedGlobal = JSON.parse(localStorage.getItem('wx_selected_global') || '{}');
          const selectedRows = Object.keys(selectedGlobal);
          if (selectedRows.length === 0) {
            alert('请至少选择一个机场');
            loader.style.display = 'none';
            return;
          }
          title = `指定机场天气预报 - ${timeStr}`;
          const regionMap = {};
          selectedRows.forEach(name => {
            const region = selectedGlobal[name];
            if (!regionMap[region]) regionMap[region] = [];
            regionMap[region].push(name);
          });
          Object.entries(regionMap).forEach(([region, names]) => {
            const allData = getFullRegionData(region);
            const filtered = filterSelectedRows(allData, names);
            slidesData.push(filtered);
          });
        }
        if (slidesData.length === 0) {
          alert('没有可导出的数据');
          loader.style.display = 'none';
          return;
        }
        const titleSlide = pptx.addSlide();
        titleSlide.addText(title, { x: 0.5, y: 1.5, w: 9, h: 1, fontSize: 24, bold: true, align: 'center' });
        slidesData.forEach(({ region, rows, routes, airportAlerts, terminalAlerts }) => {
          const airportsOnly = rows.filter(r => !r.isAlert);
          const pages = Math.ceil(airportsOnly.length / 5);
          for (let page = 0; page < pages; page++) {
            const slide = pptx.addSlide();
            slide.addText(`${region}地区天气 - 第${page + 1}/${pages}页`, { x: 0.5, y: 0.1, w: 9, h: 0.5, fontSize: 18, bold: true, color: '2c3e50' });
            const tableData = [[
              { text: "机场", options: { bold: true, fill: { color: "2c3e50" }, color: "ffffff" } },
              { text: "机场预报", options: { bold: true, fill: { color: "2c3e50" }, color: "ffffff" } },
              { text: "终端区预报", options: { bold: true, fill: { color: "2c3e50" }, color: "ffffff" } }
            ]];
            const start = page * 5;
            const end = Math.min(start + 5, airportsOnly.length);
            for (let i = start; i < end; i++) {
              const row = airportsOnly[i];
              tableData.push([row.displayName, row.airport ? row.airport.content.replace(/！/g, '') : '', row.terminal ? row.terminal.content.replace(/！/g, '') : '']);
              const aAlert = airportAlerts.find(a => a.name.includes(row.displayName));
              const tAlert = terminalAlerts.find(t => t.name.includes(row.displayName));
              const aText = aAlert ? `机场警报：${aAlert.content.replace(/！/g, '')}` : '';
              const tText = tAlert ? `终端区警报：${tAlert.content.replace(/！/g, '')}` : '';
              if (aText || tText) {
                tableData.push(['', aText, tText]);
              }
            }
            if (page === pages - 1 && routes.length > 0) {
              const routeText = routes.map(r => `${r.name}：${r.content.replace(/！/g, '')}`).join('\n');
              tableData.push([
                { text: "航路天气", options: { bold: true, fill: { color: "f0f0f0" } } },
                { text: routeText, options: { colSpan: 2, align: 'left', fontSize: 11 } },
                ''
              ]);
            }
            slide.addTable(tableData, {
              x: 0.5, y: 0.6, w: 9, colW: [1.5, 3.75, 3.75], rowH: [0.5, ...Array(tableData.length - 1).fill(0.7)],
              border: { type: "solid", pt: 1, color: "cccccc" },
              fontSize: 11, valign: "middle", margin: 0.05
            });
          }
        });
        const fileName = `预报_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}.pptx`;
        pptx.writeFile({ fileName })
          .then(() => alert(`PPT已成功生成: ${fileName}`))
          .catch(err => alert('PPT生成失败: ' + err))
          .finally(() => loader.style.display = 'none');
      } catch (err) {
        alert('生成PPT时出错: ' + err.message);
        loader.style.display = 'none';
      }
    }
    function getFullRegionData(region) {
      const original = CURRENT_REGION;
      CURRENT_REGION = region;
      const airports = filterByRegion(ALL_DATA.airports || []);
      const terminals = filterByRegion(ALL_DATA.terminal_areas || []);
      const airportAlerts = filterByRegion(ALL_DATA.airport_alerts || []);
      const terminalAlerts = filterByRegion(ALL_DATA.terminal_alerts || []);
      const routes = filterByRegion(ALL_DATA.routes || []);
      const cityMap = new Map();
      [...airports, ...terminals].forEach(item => {
        const key = getDisplayName(item.name);
        const hasAlert = airportAlerts.some(a => getDisplayName(a.name) === key) || terminalAlerts.some(t => getDisplayName(t.name) === key);
        if ((!item.content || item.content.trim() === '' || item.content.includes('，无')) && !hasAlert) return;
        if (!cityMap.has(key)) cityMap.set(key, { displayName: key, airport: null, terminal: null });
        if (airports.some(a => getDisplayName(a.name) === key)) cityMap.get(key).airport = item;
        if (terminals.some(t => getDisplayName(t.name) === key)) cityMap.get(key).terminal = item;
      });
      [...airportAlerts, ...terminalAlerts].forEach(alert => {
        const key = getDisplayName(alert.name);
        if (!cityMap.has(key)) cityMap.set(key, { displayName: key, airport: null, terminal: null });
      });
      const rows = [...cityMap.values()]
        .sort((a, b) => a.displayName.localeCompare(b.displayName, 'zh-CN'))
        .map(row => ({ ...row, isAlert: false }));
      CURRENT_REGION = original;
      return { region, rows, routes, airportAlerts, terminalAlerts };
    }
    function filterSelectedRows(regionData, selectedRows) {
      const filtered = regionData.rows.filter(row => selectedRows.includes(row.displayName));
      return { ...regionData, rows: filtered };
    }
    let CURRENT_REGION='华北';
    let ALL_DATA={};
    document.addEventListener('DOMContentLoaded',()=>{
      document.querySelectorAll('.region-selector button').forEach(btn=>{
        const dot=document.createElement('span'); dot.className='region-dot'; dot.style.display='none'; btn.prepend(dot);
      });
      loadData(false);
      const saveInitialSnapshot = () => {
        if (Object.keys(ALL_DATA).length > 0) {
          localStorage.setItem('wx_initial_snapshot', JSON.stringify(ALL_DATA));
        } else {
          setTimeout(saveInitialSnapshot, 500);
        }
      };
      setTimeout(saveInitialSnapshot, 1000);
      document.getElementById('btnRefresh').onclick=()=>loadData(true);
      document.querySelectorAll('.region-selector button').forEach(btn => {
  btn.onclick = () => {
    CURRENT_REGION = btn.dataset.region;
    document.querySelectorAll('.region-selector button').forEach(b => b.classList.toggle('active', b === btn));
    setTimeout(() => {
      const allId = `selectAll_${CURRENT_REGION}`;
      const savedAll = JSON.parse(localStorage.getItem(allId) || 'false');
      const cbAll = document.getElementById(allId);
      if (cbAll) cbAll.checked = savedAll;
    }, 0);
    render();
    loadSigWx();
  };
});
      window.addEventListener('keydown',e=>{if((e.ctrlKey||e.metaKey)&&e.key.toLowerCase()==='r'){e.preventDefault();loadData(true);}});
      window.addEventListener('beforeunload',showGlobalLoader);
    });
  </script>
</body>
</html>